#!/usr/bin/env python3
"""
测试 config.py 中的数据库连接问题
"""

import pymysql
import time

def test_db_connection_with_timeout():
    """测试带超时的数据库连接"""
    print("测试带超时的数据库连接...")
    
    # 生产环境配置
    host = "*************"
    port = 4273
    user = "copilot"
    password = "gB8vyT_^6jW^"
    database = "svwcopilot"
    
    try:
        start_time = time.time()
        
        # 添加连接超时
        db_connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            connect_timeout=10,  # 10秒连接超时
            read_timeout=30,     # 30秒读取超时
            write_timeout=30     # 30秒写入超时
        )
        
        cursor = db_connection.cursor()
        print("### 获取cskb_agent_key参数")
        
        querySQL = """SELECT agent_id,agent_name,access_token,env FROM cskb_agent_key"""
        cursor.execute(querySQL)
        result = cursor.fetchall()
        
        print(f"查询成功，获取到 {len(result)} 条记录")
        
        # 处理结果
        cskb_agent_key = {}
        for row in result:
            agent_id = row[0]
            agent_name = row[1]
            access_token = row[2]
            env = row[3]
            
            key = f'[{env}] {agent_name}'
            cskb_agent_key[key] = {"agent_id": agent_id, "access_token": access_token}
        
        print(f"处理完成，共 {len(cskb_agent_key)} 个agent配置")
        
        cursor.close()
        db_connection.close()
        
        end_time = time.time()
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        
        return cskb_agent_key
        
    except Exception as error:
        print(f"## 获取cskb_agent_key参数：数据库操作失败: {error}")
        return {}

def test_db_connection_without_timeout():
    """测试不带超时的数据库连接（模拟原始问题）"""
    print("测试不带超时的数据库连接...")
    
    # 生产环境配置
    host = "*************"
    port = 4273
    user = "copilot"
    password = "gB8vyT_^6jW^"
    database = "svwcopilot"
    
    try:
        start_time = time.time()
        
        # 不添加连接超时（模拟原始代码）
        db_connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        
        cursor = db_connection.cursor()
        print("### 获取cskb_agent_key参数")
        
        querySQL = """SELECT agent_id,agent_name,access_token,env FROM cskb_agent_key"""
        cursor.execute(querySQL)
        result = cursor.fetchall()
        
        print(f"查询成功，获取到 {len(result)} 条记录")
        
        cursor.close()
        db_connection.close()
        
        end_time = time.time()
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        
    except Exception as error:
        print(f"## 获取cskb_agent_key参数：数据库操作失败: {error}")

if __name__ == "__main__":
    print("=" * 50)
    print("开始测试数据库连接问题")
    print("=" * 50)
    
    # 测试带超时的连接
    result = test_db_connection_with_timeout()
    
    print("\n" + "=" * 50)
    print("测试不带超时的连接")
    print("=" * 50)
    
    # 测试不带超时的连接
    test_db_connection_without_timeout()
